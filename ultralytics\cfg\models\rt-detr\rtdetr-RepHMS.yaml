# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]]
  - [-1, 1, Conv, [128, 3, 2]]
  - [-1, 1, RepHMS, [128, 2, 1, 3, 3]] # 2-P2/4
  - [-1, 1, Conv, [256, 3, 2]]
  - [-1, 1, RepHMS, [256, 3, 1, 3, 5]] # 4-P3/8
  - [-1, 1, Conv, [512, 3, 2]]
  - [-1, 1, <PERSON><PERSON><PERSON>, [512, 3, 1, 3, 7]] # 6-P4/16
  - [-1, 1, Conv, [1024, 3, 2]]
  - [-1, 1, Rep<PERSON><PERSON>, [1024, 2, 1, 3, 9]] # 8-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 9 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] 
  - [-1, 1, Conv, [256, 1, 1]] # 11 P5/32

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] 
  - [6, 1, Conv, [256, 1, 1, None, 1, 1, False]]  
  - [[-2, -1], 1, Concat, [1]] 
  - [-1, 3, RepC3, [256, 0.5]]  
  - [-1, 1, Conv, [256, 1, 1]]  # 16 P4/16

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']]
  - [4, 1, Conv, [256, 1, 1, None, 1, 1, False]] 
  - [[-2, -1], 1, Concat, [1]]  
  - [-1, 3, RepC3, [256, 0.5]] # 20 P3/8   

  - [-1, 1, Conv, [256, 3, 2]]   
  - [[-1, 16], 1, Concat, [1]]  
  - [-1, 3, RepC3, [256, 0.5]] # 23 P4/16

  - [-1, 1, Conv, [256, 3, 2]]   
  - [[-1, 11], 1, Concat, [1]]  
  - [-1, 3, RepC3, [256, 0.5]] # 26 P5/32

  - [[20, 23, 26], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5)