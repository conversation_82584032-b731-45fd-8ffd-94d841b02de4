# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON>ayer, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, <PERSON><PERSON><PERSON>, 2, 2, 'relu']] # 4
  - [-1, 1, Blocks, [128, <PERSON><PERSON><PERSON>, 2, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BasicBlock, 2, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 7-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 8 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] # 9
  - [-1, 1, Conv, [256, 1, 1]]  # 10, Y5, lateral_convs.0

  - [-1, 1, ChannelAttention_HSFPN, []] # 11
  - [-1, 1, nn.Conv2d, [256, 1]] # 12
  - [-1, 1, nn.ConvTranspose2d, [256, 3, 2, 1, 1]] # 13

  - [6, 1, ChannelAttention_HSFPN, []] # 14
  - [-1, 1, nn.Conv2d, [256, 1]] # 15
  - [13, 1, ChannelAttention_HSFPN, [4, False]] # 16
  - [[-1, -2], 1, Multiply, []] # 17
  - [[-1, 13], 1, Add, []] # 18
  - [-1, 3, RepC3, [256, 0.5]] # 19 P4/16

  - [13, 1, nn.ConvTranspose2d, [256, 3, 2, 1, 1, 16]] # 20
  - [5, 1, ChannelAttention_HSFPN, []] # 21
  - [-1, 1, nn.Conv2d, [256, 1]] # 22
  - [20, 1, ChannelAttention_HSFPN, [4, False]] # 23
  - [[-1, -2], 1, Multiply, []] # 24
  - [[-1, 20], 1, Add, []] # 25
  - [-1, 3, RepC3, [256, 0.5]] # 26 P3/8

  - [-1, 1, nn.Conv2d, [256, 3, 2, 1]] # 27
  - [19, 1, ChannelAttention_HSFPN, []] # 28
  - [-1, 1, nn.Conv2d, [256, 1]] # 29
  - [27, 1, ChannelAttention_HSFPN, [4, False]] # 30
  - [[-1, -2], 1, Multiply, []] # 31
  - [[-1, 27], 1, Add, []] # 32
  - [-1, 3, RepC3, [256, 0.5]] # 33 P4/16

  - [27, 1, nn.Conv2d, [256, 3, 2, 1]] # 34
  - [12, 1, ChannelAttention_HSFPN, []] # 35
  - [-1, 1, nn.Conv2d, [256, 1]] # 36
  - [34, 1, ChannelAttention_HSFPN, [4, False]] # 37
  - [[-1, -2], 1, Multiply, []] # 38
  - [[-1, 34], 1, Add, []] # 39
  - [-1, 3, RepC3, [256, 0.5]] # 40 P5/32

  - [[26, 33, 40], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5)
