
nc: 80  # number of classes
scales:   # [depth, width, max_channels]
  T: [0.33, 0.25, 1024]  #<PERSON>mba-YOLOv8-T summary: 6.1M parameters,   14.3GFLOPs


# Mamba-YOLO backbone 
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON><PERSON><PERSON>, [128, 3]]   # 0-P2/4
  - [-1, 3, <PERSON><PERSON><PERSON><PERSON>_YOLO, [128]]               # 1
  - [-1, 1, <PERSON><PERSON>lueMerge, [256]]      # 2 p3/8
  - [-1, 3, V<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>, [256]]              # 3
  - [-1, 1, <PERSON><PERSON><PERSON>M<PERSON><PERSON>, [512]]      # 4 p4/16
  - [-1, 9, V<PERSON><PERSON><PERSON>_<PERSON><PERSON>O, [512]]              # 5
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [1024]]      # 6 p5/32
  - [-1, 3, V<PERSON>B<PERSON>_YOLO, [1024]]              # 7
  - [-1, 1, SPPF, [1024, 5]]               # 8

# <PERSON><PERSON>-YOLO PAFPN
head:
  - [-1, 1, nn.<PERSON><PERSON><PERSON>, [None, 2, 'nearest']]
  - [[-1, 5], 1, <PERSON><PERSON>, [1]]  # cat backbone P4
  - [-1, 3, <PERSON><PERSON><PERSON><PERSON>, [512]]  # 11

  - [-1, 1, nn.<PERSON><PERSON><PERSON>, [None, 2, 'nearest']]
  - [[-1, 3], 1, Concat, [1]]  # cat backbone P3
  - [-1, 3, XSSBlock, [256]]  # 14 (P3/8-small)

  - [-1, 1, Conv, [256, 3, 2]]
  - [[-1, 11], 1, Concat, [1]]  # cat head P4
  - [-1, 3, XSSBlock, [512]]  # 17 (P4/16-medium)

  - [-1, 1, Conv, [512, 3, 2]]
  - [[-1, 8], 1, Concat, [1]]  # cat head P5
  - [-1, 3, XSSBlock, [1024]]  # 20 (P5/32-large)

  - [[14, 17, 20], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5)
