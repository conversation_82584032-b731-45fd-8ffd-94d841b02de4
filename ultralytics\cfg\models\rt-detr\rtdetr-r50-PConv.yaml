# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON><PERSON>er, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, <PERSON><PERSON><PERSON><PERSON>_PConv, 3, 2, 'relu']] # 4
  - [-1, 1, Block<PERSON>, [128, <PERSON><PERSON><PERSON>eck_<PERSON>onv, 4, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BottleNeck_PConv, 6, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BottleNeck_PConv, 3, 5, 'relu']] # 7-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 8 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] # 9
  - [-1, 1, Conv, [256, 1, 1]]  # 10, Y5, lateral_convs.0

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 11
  - [6, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 12 input_proj.1
  - [[-2, -1], 1, Concat, [1]] # 13
  - [-1, 3, RepC3, [256]]  # 14, fpn_blocks.0
  - [-1, 1, Conv, [256, 1, 1]]   # 15, Y4, lateral_convs.1

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 16
  - [5, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 17 input_proj.0
  - [[-2, -1], 1, Concat, [1]]  # 18 cat backbone P4
  - [-1, 3, RepC3, [256]]    # X3 (19), fpn_blocks.1

  - [-1, 1, Conv, [256, 3, 2]]   # 20, downsample_convs.0
  - [[-1, 15], 1, Concat, [1]]  # 21 cat Y4
  - [-1, 3, RepC3, [256]]    # F4 (22), pan_blocks.0

  - [-1, 1, Conv, [256, 3, 2]]   # 23, downsample_convs.1
  - [[-1, 10], 1, Concat, [1]]  # 24 cat Y5
  - [-1, 3, RepC3, [256]]    # F5 (25), pan_blocks.1

  - [[19, 22, 25], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 6]]  # Detect(P3, P4, P5)
