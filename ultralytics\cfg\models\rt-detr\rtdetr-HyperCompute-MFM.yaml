# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON>ayer, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, <PERSON><PERSON><PERSON>, 2, 2, 'relu']] # 4
  - [-1, 1, Blocks, [128, <PERSON><PERSON><PERSON>, 2, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BasicBlock, 2, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 7-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 8 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] # 9
  - [-1, 1, Conv, [256, 1, 1]]  # 10, Y5, lateral_convs.0

  # Semantic Collecting
  - [2, 1, nn.AvgPool2d, [8, 8, 0]] # 11
  - [4, 1, nn.AvgPool2d, [4, 4, 0]] # 12
  - [5, 1, nn.AvgPool2d, [2, 2, 0]] # 13
  - [10, 1, nn.Upsample, [None, 2, 'nearest']] # 14
  - [[11, 12, 13, 6, 14], 1, MFM, [256]]  # cat 15

  # Hypergraph Computation
  - [-1, 1, Conv, [256, 1, 1]] # 16
  - [-1, 1, HyperComputeModule, [256, 10]] # 17
  - [-1, 3, MANet, [256, True, 2, 3]] # 18

  # Semantic Collecting
  - [-1, 1, nn.AvgPool2d, [2, 2, 0]] # 19
  - [[-1, 10], 1, MFM, [256]]  # cat 20
  - [-1, 1, Conv, [1024, 1, 1]] # 21 P5

  - [6, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 22 input_proj.1
  - [[18, -1], 1, MFM, [256]] # 23
  - [-1, 3, RepC3, [256, 0.5]]  # 24, fpn_blocks.0
  - [-1, 1, Conv, [256, 1, 1]]   # 25, Y4, lateral_convs.1

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 26
  - [5, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 27 input_proj.0
  - [[-2, -1], 1, MFM, [256]]  # 28 cat backbone P4
  - [-1, 3, RepC3, [256, 0.5]]    # X3 (29), fpn_blocks.1

  - [-1, 1, Conv, [256, 3, 2]]   # 30, downsample_convs.0
  - [[-1, 25], 1, MFM, [256]]  # 31 cat Y4
  - [-1, 3, RepC3, [256, 0.5]]    # F4 (32), pan_blocks.0

  - [-1, 1, Conv, [256, 3, 2]]   # 33, downsample_convs.1
  - [[-1, 21], 1, MFM, [256]]  # 34 cat Y5
  - [-1, 3, RepC3, [256, 0.5]]    # F5 (35), pan_blocks.1

  - [[29, 32, 35], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5)
