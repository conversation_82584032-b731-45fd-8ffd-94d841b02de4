# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON>ayer, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, <PERSON><PERSON><PERSON>, 2, 2, 'relu']] # 4
  - [-1, 1, Blocks, [128, <PERSON><PERSON><PERSON>, 2, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BasicBlock, 2, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 7-P5/32
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 8-P6/64

head:
  - [[5, 6, 7, 8], 1, ChannelTransformer, []] # 9
  - [9, 1, GetIndexOutput, [0]] # 10-P3/8
  - [9, 1, GetIndexOutput, [1]] # 11-P4/16
  - [9, 1, GetIndexOutput, [2]] # 12-P5/32
  - [9, 1, GetIndexOutput, [3]] # 13-P6/64

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 14
  - [12, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 15 input_proj.1
  - [[-2, -1], 1, Concat, [1]] # 16
  - [-1, 3, RepC3, [256, 0.5]]  # 17, fpn_blocks.0
  - [-1, 1, Conv, [256, 1, 1]]   # 18, Y4, lateral_convs.1

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 19
  - [11, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 20 input_proj.0
  - [[-2, -1], 1, Concat, [1]]  # 21 cat backbone P4
  - [-1, 3, RepC3, [256, 0.5]]    # X3 (22), fpn_blocks.1

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 23
  - [10, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 24 input_proj.0
  - [[-2, -1], 1, Concat, [1]]  # 25 cat backbone P4
  - [-1, 3, RepC3, [256, 0.5]]    # X3 (26), fpn_blocks.1

  - [-1, 1, Conv, [256, 3, 2]]   # 27, downsample_convs.0
  - [[-1, 22], 1, Concat, [1]]  # 28 cat Y4
  - [-1, 3, RepC3, [256, 0.5]]    # F4 (29), pan_blocks.0

  - [-1, 1, Conv, [256, 3, 2]]   # 30, downsample_convs.1
  - [[-1, 18], 1, Concat, [1]]  # 31 cat Y5
  - [-1, 3, RepC3, [256, 0.5]]    # F5 (32), pan_blocks.1

  - [-1, 1, Conv, [256, 3, 2]]   # 33, downsample_convs.1
  - [[-1, 13], 1, Concat, [1]]  # 34 cat Y5
  - [-1, 3, RepC3, [256, 0.5]]    # F5 (35), pan_blocks.1

  - [[26, 29, 32, 35], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5, P6)