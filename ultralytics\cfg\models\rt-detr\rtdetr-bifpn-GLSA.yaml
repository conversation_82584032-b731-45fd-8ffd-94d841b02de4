# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]
fusion_mode: bifpn
node_mode: RepC3
head_channel: 128

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv<PERSON><PERSON><PERSON>ayer, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, Conv<PERSON><PERSON><PERSON>ayer, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, BasicBlock, 2, 2, 'relu']] # 4
  - [-1, 1, Blocks, [128, BasicBlock, 2, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BasicBlock, 2, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 7-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 8 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] # 9
  - [-1, 1, Conv, [256, 1, 1]]  # 10, Y5, lateral_convs.0

  - [5, 1, GLSA, [head_channel]]  # 11-P3/8
  - [6, 1, GLSA, [head_channel]]  # 12-P4/16
  - [10, 1, GLSA, [head_channel]]  # 13-P5/32

  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 14 P5->P4
  - [[-1, 12], 1, Fusion, [fusion_mode]] # 15
  - [-1, 3, node_mode, [head_channel, 0.5]] # 16-P4/16
  
  - [-1, 1, nn.Upsample, [None, 2, 'nearest']] # 17 P4->P3
  - [[-1, 11], 1, Fusion, [fusion_mode]] # 18
  - [-1, 3, node_mode, [head_channel, 0.5]] # 19-P3/8

  - [4, 1, Conv, [head_channel, 3, 2]] # 20 P2->P3
  - [[-1, 11, 19], 1, Fusion, [fusion_mode]] # 21
  - [-1, 3, node_mode, [head_channel, 0.5]] # 22-P3/8

  - [-1, 1, Conv, [head_channel, 3, 2]] # 23 P3->P4
  - [[-1, 12, 16], 1, Fusion, [fusion_mode]] # 24
  - [-1, 3, node_mode, [head_channel, 0.5]] # 25-P4/16

  - [-1, 1, Conv, [head_channel, 3, 2]] # 26 P4->P5
  - [[-1, 13], 1, Fusion, [fusion_mode]] # 27
  - [-1, 3, node_mode, [head_channel, 0.5]] # 28-P5/32

  - [[22, 25, 28], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5)