# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON>ayer, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, <PERSON><PERSON><PERSON>, 2, 2, 'relu']] # 4
  - [-1, 1, Blocks, [128, <PERSON><PERSON><PERSON>, 2, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BasicBlock, 2, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 7-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 8 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] # 9
  - [-1, 1, Conv, [256, 1, 1]]  # 10, Y5, lateral_convs.0

  - [[4, 5, 6, 7], 1, SimFusion_4in, []] # 11
  - [-1, 1, IFM, [[64, 32]]] # 12

  - [10, 1, Conv, [256, 1, 1]] # 13
  - [[5, 6, -1], 1, SimFusion_3in, [256]] # 14
  - [[-1, 12], 1, InjectionMultiSum_Auto_pool, [256, [64, 32], 0]] # 15
  - [-1, 3, RepC3, [256, 0.5]] # 16

  - [6, 1, Conv, [256, 1, 1]] # 17
  - [[4, 5, -1], 1, SimFusion_3in, [256]] # 18
  - [[-1, 12], 1, InjectionMultiSum_Auto_pool, [256, [64, 32], 1]] # 19
  - [-1, 3, RepC3, [256, 0.5]] # 20

  - [[20, 16, 10], 1, PyramidPoolAgg, [352, 2]] # 21
  - [-1, 1, TopBasicLayer, [352, [64, 128]]] # 22

  - [[20, 17], 1, AdvPoolFusion, []] # 23
  - [[-1, 22], 1, InjectionMultiSum_Auto_pool, [256, [64, 128], 0]] # 24
  - [-1, 3, RepC3, [256, 0.5]] # 25

  - [[-1, 13], 1, AdvPoolFusion, []] # 26
  - [[-1, 22], 1, InjectionMultiSum_Auto_pool, [256, [64, 128], 1]] # 27
  - [-1, 3, RepC3, [256, 0.5]] # 28

  - [[20, 25, 28], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]] # 29