# Ultralytics YOLO 🚀, AGPL-3.0 license
# RT-DETR-l object detection model with P3-P5 outputs. For details see https://docs.ultralytics.com/models/rtdetr

# Parameters
nc: 80  # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-cls.yaml' will call yolov8-cls.yaml with scale 'n'
  # [depth, width, max_channels]
  l: [1.00, 1.00, 1024]

# From BiliBili 魔鬼面具
backbone:
  # [from, repeats, module, args]
  - [-1, 1, <PERSON>v<PERSON><PERSON><PERSON>ayer, [32, 3, 2, None, False, 'relu']] # 0-P1/2
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [32, 3, 1, None, False, 'relu']] # 1
  - [-1, 1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, [64, 3, 1, None, False, 'relu']] # 2
  - [-1, 1, nn.<PERSON><PERSON>ool2d, [3, 2, 1]] # 3-P2/4
  
  # [ch_out, block_type, block_nums, stage_num, act, variant]
  - [-1, 1, Blocks, [64, <PERSON><PERSON><PERSON>, 2, 2, 'relu']] # 4
  - [-1, 1, Blocks, [128, <PERSON><PERSON><PERSON>, 2, 3, 'relu']] # 5-P3/8
  - [-1, 1, Blocks, [256, BasicBlock, 2, 4, 'relu']] # 6-P4/16
  - [-1, 1, Blocks, [512, BasicBlock, 2, 5, 'relu']] # 7-P5/32

head:
  - [-1, 1, Conv, [256, 1, 1, None, 1, 1, False]]  # 8 input_proj.2
  - [-1, 1, AIFI, [1024, 8]] # 9
  - [-1, 1, Conv, [256, 1, 1]]  # 10, Y5, lateral_convs.0

  - [6, 1, Conv, [256, 3, 2]] # 11-P5/32
  - [[-1, 10], 1, MFM, [256]] # 12
  - [-1, 3, RepC3, [256, 0.5]] # 13-P5/32

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 14-P4/16
  - [5, 1, Conv, [128, 3, 2]] # 15-P4/16
  - [[-1, -2, 6], 1, MFM, [256]] # 16
  - [-1, 3, RepC3, [256, 0.5]] # 17-P4/16

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]] # 18-P3/8
  - [4, 1, Conv, [64, 3, 2]] # 19-P3/8
  - [[-1, -2, 5], 1, MFM, [128]] # 20
  - [-1, 3, RepC3, [128, 0.5]] # 21-P3/8

  - [[18, -1], 1, MFM, [256]] # 22
  - [-1, 3, RepC3, [128, 0.5]] # 23-P3/8

  - [21, 1, Conv, [256, 3, 2]] # 24-P4/16
  - [23, 1, Conv, [256, 3, 2]] # 25-P4/16
  - [[-1, -2, 17, 14], 1, MFM, [256]] # 26-P4/16
  - [-1, 3, RepC3, [256, 0.5]] # 27-P4/16

  - [17, 1, Conv, [512, 3, 2]] # 28-P5/32
  - [27, 1, Conv, [512, 3, 2]] # 29-P5/32
  - [[-1, -2, 13], 1, MFM, [256]] # 30-P5/32
  - [-1, 3, RepC3, [256, 0.5]] # 31-P5/32

  - [[23, 27, 31], 1, RTDETRDecoder, [nc, 256, 300, 4, 8, 3]]  # Detect(P3, P4, P5)
